"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"

export default function Home() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-lg">Loading...</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto text-center">
      <h1 className="text-4xl font-bold text-gray-800 mb-6">
        Welcome to NextApp
      </h1>

      <p className="text-xl text-gray-600 mb-8">
        A Next.js application with authentication using NextAuth.js and Prisma
      </p>

      {session ? (
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Hello, {session.user?.name}!
          </h2>
          <p className="text-gray-600 mb-6">
            You are successfully logged in. You can now access your dashboard.
          </p>
          <Link
            href="/dashboard"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
          >
            Go to Dashboard
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-md p-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Get Started
          </h2>
          <p className="text-gray-600 mb-6">
            Sign up for a new account or log in to access your dashboard.
          </p>
          <div className="space-x-4">
            <Link
              href="/signup"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Sign Up
            </Link>
            <Link
              href="/login"
              className="inline-block bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Log In
            </Link>
          </div>
        </div>
      )}

      <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            🔐 Secure Authentication
          </h3>
          <p className="text-gray-600">
            Built with NextAuth.js for secure user authentication and session management.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            🗄️ Database Integration
          </h3>
          <p className="text-gray-600">
            Uses Prisma ORM with SQLite for efficient data management.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            ⚡ Modern Stack
          </h3>
          <p className="text-gray-600">
            Built with Next.js 15, TypeScript, and Tailwind CSS for a modern development experience.
          </p>
        </div>
      </div>
    </div>
  )
}
